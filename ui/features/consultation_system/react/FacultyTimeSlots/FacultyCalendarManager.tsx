import React, { useState, useEffect } from 'react'
import { View } from '@instructure/ui-view'
import { Flex } from '@instructure/ui-flex'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Icon<PERSON>utton } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Spinner } from '@instructure/ui-spinner'
import { Modal } from '@instructure/ui-modal'
import { FormFieldGroup } from '@instructure/ui-form-field'
import { DateInput } from '@instructure/ui-date-input'
import { TimeInput } from '@instructure/ui-time-input'
import { Checkbox } from '@instructure/ui-checkbox'
import CanvasSelect from '@canvas/instui-bindings/react/Select'
import { IconArrowOpenStartLine, IconArrowOpenEndLine, IconAddLine, IconTrashLine, IconEditLine, IconCalendarMonthLine } from '@instructure/ui-icons'
import axios from 'axios'
import { showFlashSuccess, showFlashError } from '@canvas/alerts/react/FlashAlert'

interface FacultyTimeSlot {
  id: string
  start_time: string
  end_time: string
  is_available: boolean
  is_booked: boolean
  created_at: string
  updated_at: string
}

interface FacultyCalendarManagerProps {
  facultyId: string
  facultyName: string
}

const FacultyCalendarManager: React.FC<FacultyCalendarManagerProps> = ({
  facultyId,
  facultyName
}) => {
  const [currentWeek, setCurrentWeek] = useState(new Date())
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth())
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [timeSlots, setTimeSlots] = useState<FacultyTimeSlot[]>([])
  const [loading, setLoading] = useState(false)
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [selectedSlot, setSelectedSlot] = useState<FacultyTimeSlot | null>(null)
  
  // New time slot form state
  const [newSlotDate, setNewSlotDate] = useState<Date | null>(null)
  const [newSlotStartTime, setNewSlotStartTime] = useState('')
  const [newSlotEndTime, setNewSlotEndTime] = useState('')
  const [newSlotIsAvailable, setNewSlotIsAvailable] = useState(true)
  const [isRecurring, setIsRecurring] = useState(false)
  const [recurringWeeks, setRecurringWeeks] = useState(4)
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})

  // Generate week dates
  const getWeekDates = (date: Date) => {
    const week = []
    const startOfWeek = new Date(date)
    const day = startOfWeek.getDay()
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1) // Monday as first day
    startOfWeek.setDate(diff)

    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek)
      day.setDate(startOfWeek.getDate() + i)
      week.push(day)
    }
    return week
  }

  const weekDates = getWeekDates(currentWeek)

  // Navigation functions
  const goToPreviousWeek = () => {
    const prevWeek = new Date(currentWeek)
    prevWeek.setDate(currentWeek.getDate() - 7)
    setCurrentWeek(prevWeek)
  }

  const goToNextWeek = () => {
    const nextWeek = new Date(currentWeek)
    nextWeek.setDate(currentWeek.getDate() + 7)
    setCurrentWeek(nextWeek)
  }

  const goToSelectedMonth = () => {
    const newDate = new Date(selectedYear, selectedMonth, 1)
    setCurrentWeek(newDate)
  }

  // Fetch time slots for the current week
  useEffect(() => {
    if (!facultyId) return

    const fetchTimeSlots = async () => {
      setLoading(true)
      try {
        const startDate = weekDates[0].toISOString().split('T')[0]
        const endDate = weekDates[6].toISOString().split('T')[0]
        
        const response = await axios.get(
          `/consultation_requests/faculty/${facultyId}/time_slots?start_date=${startDate}&end_date=${endDate}`
        )
        
        setTimeSlots(response.data.time_slots || [])
      } catch (error) {
        console.error('Error fetching time slots:', error)
        showFlashError('Failed to load time slots. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    fetchTimeSlots()
  }, [facultyId, currentWeek])

  // Generate time slots for display (7 AM to 8 PM)
  const generateTimeSlots = () => {
    const slots = []
    for (let hour = 7; hour <= 20; hour++) {
      slots.push(`${hour.toString().padStart(2, '0')}:00`)
      if (hour < 20) {
        slots.push(`${hour.toString().padStart(2, '0')}:30`)
      }
    }
    return slots
  }

  const timeSlotHours = generateTimeSlots()

  // Get slots for specific date and time
  const getSlotsForDateTime = (date: Date, time: string) => {
    const dateStr = date.toISOString().split('T')[0]
    const [hour, minute] = time.split(':')
    
    return timeSlots.filter(slot => {
      const slotDate = new Date(slot.start_time)
      return (
        slotDate.toISOString().split('T')[0] === dateStr &&
        slotDate.getHours() === parseInt(hour) &&
        slotDate.getMinutes() === parseInt(minute)
      )
    })
  }

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    })
  }

  // Handle adding a new time slot
  const handleAddTimeSlot = async () => {
    // Validate form
    const errors: Record<string, string> = {}
    
    if (!newSlotDate) {
      errors.date = 'Please select a date'
    }
    
    if (!newSlotStartTime) {
      errors.startTime = 'Please select a start time'
    }
    
    if (!newSlotEndTime) {
      errors.endTime = 'Please select an end time'
    } else if (newSlotStartTime && newSlotEndTime <= newSlotStartTime) {
      errors.endTime = 'End time must be after start time'
    }
    
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      return
    }
    
    setFormErrors({})
    setLoading(true)
    
    try {
      // Create dates for API
      const dates = []
      
      if (isRecurring) {
        // Create recurring slots
        for (let i = 0; i < recurringWeeks; i++) {
          const date = new Date(newSlotDate!)
          date.setDate(date.getDate() + (i * 7))
          dates.push(date)
        }
      } else {
        dates.push(newSlotDate!)
      }
      
      // Create time slots for each date
      for (const date of dates) {
        const [startHour, startMinute] = newSlotStartTime.split(':').map(Number)
        const [endHour, endMinute] = newSlotEndTime.split(':').map(Number)
        
        const startTime = new Date(date)
        startTime.setHours(startHour, startMinute, 0)
        
        const endTime = new Date(date)
        endTime.setHours(endHour, endMinute, 0)
        
        await axios.post('/faculty_time_slots', {
          faculty_time_slot: {
            start_time: startTime.toISOString(),
            end_time: endTime.toISOString(),
            is_available: newSlotIsAvailable
          }
        })
      }
      
      // Refresh time slots
      const startDate = weekDates[0].toISOString().split('T')[0]
      const endDate = weekDates[6].toISOString().split('T')[0]
      
      const response = await axios.get(
        `/consultation_requests/faculty/${facultyId}/time_slots?start_date=${startDate}&end_date=${endDate}`
      )
      
      setTimeSlots(response.data.time_slots || [])
      
      // Reset form
      setNewSlotDate(null)
      setNewSlotStartTime('')
      setNewSlotEndTime('')
      setNewSlotIsAvailable(true)
      setIsRecurring(false)
      setRecurringWeeks(4)
      
      // Close modal
      setIsAddModalOpen(false)
      
      showFlashSuccess('Time slot(s) added successfully')
    } catch (error) {
      console.error('Error adding time slot:', error)
      showFlashError('Failed to add time slot. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Handle deleting a time slot
  const handleDeleteTimeSlot = async (slotId: string) => {
    if (!confirm('Are you sure you want to delete this time slot?')) {
      return
    }
    
    setLoading(true)
    
    try {
      await axios.delete(`/api/v1/faculty_time_slots/${slotId}`)
      
      // Remove from state
      setTimeSlots(prev => prev.filter(slot => slot.id !== slotId))
      
      showFlashSuccess('Time slot deleted successfully')
    } catch (error) {
      console.error('Error deleting time slot:', error)
      showFlashError('Failed to delete time slot. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Generate month options
  const monthOptions = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  // Generate year options (current year ± 2)
  const currentYear = new Date().getFullYear()
  const yearOptions = []
  for (let year = currentYear - 1; year <= currentYear + 2; year++) {
    yearOptions.push(year)
  }

  return (
    <View as="div">
      <Heading level="h2" margin="0 0 medium 0">
        Manage Consultation Time Slots
      </Heading>
      
      <View as="div" margin="0 0 medium 0">
        <Button
          renderIcon={<IconAddLine />}
          color="primary"
          onClick={() => {
            setNewSlotDate(new Date())
            setIsAddModalOpen(true)
          }}
        >
          Add New Time Slot
        </Button>
      </View>
      
      <View as="div" borderWidth="small" borderRadius="medium">
        {/* Calendar Navigation */}
        <View as="div" padding="medium" borderWidth="0 0 small 0">
          <Flex justifyItems="space-between" alignItems="center">
            <Flex.Item>
              <Flex gap="small" alignItems="center">
                <IconButton
                  screenReaderLabel="Previous week"
                  onClick={goToPreviousWeek}
                  size="small"
                >
                  <IconArrowOpenStartLine />
                </IconButton>
                
                <Text weight="bold" size="large">
                  {weekDates[0].toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </Text>
                
                <IconButton
                  screenReaderLabel="Next week"
                  onClick={goToNextWeek}
                  size="small"
                >
                  <IconArrowOpenEndLine />
                </IconButton>
              </Flex>
            </Flex.Item>
            
            <Flex.Item>
              <Flex gap="small" alignItems="center">
                <View as="div" margin="0 small 0 0">
                  <CanvasSelect
                    id="month-select"
                    label="Month"
                    value={selectedMonth.toString()}
                    onChange={(_e, value) => setSelectedMonth(parseInt(value))}
                  >
                    {monthOptions.map((month, index) => (
                      <option key={index} value={index.toString()}>
                        {month}
                      </option>
                    ))}
                  </CanvasSelect>
                </View>
                
                <View as="div" margin="0 small 0 0">
                  <CanvasSelect
                    id="year-select"
                    label="Year"
                    value={selectedYear.toString()}
                    onChange={(_e, value) => setSelectedYear(parseInt(value))}
                  >
                    {yearOptions.map(year => (
                      <option key={year} value={year.toString()}>
                        {year}
                      </option>
                    ))}
                  </CanvasSelect>
                </View>
                
                <Button onClick={goToSelectedMonth} size="small">
                  Go
                </Button>
              </Flex>
            </Flex.Item>
          </Flex>
        </View>

        {/* Calendar Grid */}
        <View as="div">
          {/* Week Header */}
          <Flex>
            <Flex.Item size="80px" padding="small" textAlign="center">
              <Text weight="bold" size="small">Time</Text>
            </Flex.Item>
            {weekDates.map((date, index) => (
              <Flex.Item key={index} shouldGrow padding="small" textAlign="center">
                <Text weight="bold" size="small">
                  {formatDate(date)}
                </Text>
              </Flex.Item>
            ))}
          </Flex>

          {/* Time Slots Grid */}
          {loading ? (
            <View as="div" padding="large" textAlign="center">
              <Spinner renderTitle="Loading time slots..." />
            </View>
          ) : (
            <View as="div" maxHeight="400px" overflowY="auto">
              {timeSlotHours.map((time, timeIndex) => (
                <View key={timeIndex} as="div" borderWidth="0 0 small 0">
                  <Flex>
                    <Flex.Item size="80px" padding="x-small" textAlign="center">
                      <Text size="small">{time}</Text>
                    </Flex.Item>
                    {weekDates.map((date, dateIndex) => {
                      const slotsAtTime = getSlotsForDateTime(date, time)
                      const hasSlot = slotsAtTime.length > 0
                      const slot = slotsAtTime[0] // Take first slot if multiple
                      const isPast = new Date(`${date.toISOString().split('T')[0]}T${time}:00`) < new Date()

                      return (
                        <Flex.Item
                          key={dateIndex}
                          shouldGrow
                          padding="x-small"
                        >
                          <View
                            as="div"
                            height="40px"
                            borderRadius="small"
                            padding="xx-small"
                            background={
                              hasSlot ?
                                (slot.is_booked ? "danger" :
                                 slot.is_available ? "success" : "secondary") :
                              isPast ? "secondary" : "primary"
                            }
                            textAlign="center"
                            position="relative"
                          >
                            {hasSlot && (
                              <Flex gap="xx-small" justifyItems="center" alignItems="center" height="100%">
                                <Flex.Item shouldGrow>
                                  <Text
                                    size="x-small"
                                    color="primary-inverse"
                                    weight="bold"
                                  >
                                    {slot.is_booked ? "Booked" :
                                     slot.is_available ? "Available" : "Unavailable"}
                                  </Text>
                                </Flex.Item>
                                <Flex.Item>
                                  <Flex gap="xx-small">
                                    <IconButton
                                      size="x-small"
                                      screenReaderLabel="Edit time slot"
                                      onClick={() => {
                                        setSelectedSlot(slot)
                                        setIsEditModalOpen(true)
                                      }}
                                    >
                                      <IconEditLine />
                                    </IconButton>
                                    <IconButton
                                      size="x-small"
                                      screenReaderLabel="Delete time slot"
                                      onClick={() => handleDeleteTimeSlot(slot.id)}
                                      color="danger"
                                    >
                                      <IconTrashLine />
                                    </IconButton>
                                  </Flex>
                                </Flex.Item>
                              </Flex>
                            )}

                            {!hasSlot && !isPast && (
                              <Button
                                size="x-small"
                                onClick={() => {
                                  const [hour, minute] = time.split(':').map(Number)
                                  const slotDate = new Date(date)
                                  slotDate.setHours(hour, minute, 0)
                                  setNewSlotDate(slotDate)
                                  setNewSlotStartTime(time)
                                  setNewSlotEndTime(`${hour + 1}:${minute.toString().padStart(2, '0')}`)
                                  setIsAddModalOpen(true)
                                }}
                                renderIcon={<IconAddLine />}
                                color="primary"
                              >
                                Add
                              </Button>
                            )}
                          </View>
                        </Flex.Item>
                      )
                    })}
                  </Flex>
                </View>
              ))}
            </View>
          )}
        </View>
      </View>

      {/* Add Time Slot Modal */}
      <Modal
        open={isAddModalOpen}
        onDismiss={() => setIsAddModalOpen(false)}
        size="medium"
        label="Add New Time Slot"
        shouldCloseOnDocumentClick={false}
      >
        <Modal.Header>
          <Heading level="h3">Add New Time Slot</Heading>
        </Modal.Header>

        <Modal.Body>
          <FormFieldGroup description="Create a new consultation time slot">
            <DateInput
              renderLabel="Date"
              value={newSlotDate?.toISOString().split('T')[0] || ''}
              onDateChange={(date) => setNewSlotDate(date ? new Date(date) : null)}
              messages={formErrors.date ? [{ text: formErrors.date, type: 'error' }] : []}
            />

            <Flex gap="medium">
              <Flex.Item shouldGrow>
                <TimeInput
                  renderLabel="Start Time"
                  value={newSlotStartTime}
                  onChange={(e) => setNewSlotStartTime(e.target.value)}
                  messages={formErrors.startTime ? [{ text: formErrors.startTime, type: 'error' }] : []}
                />
              </Flex.Item>

              <Flex.Item shouldGrow>
                <TimeInput
                  renderLabel="End Time"
                  value={newSlotEndTime}
                  onChange={(e) => setNewSlotEndTime(e.target.value)}
                  messages={formErrors.endTime ? [{ text: formErrors.endTime, type: 'error' }] : []}
                />
              </Flex.Item>
            </Flex>

            <Checkbox
              label="Available for booking"
              checked={newSlotIsAvailable}
              onChange={(e) => setNewSlotIsAvailable(e.target.checked)}
            />

            <Checkbox
              label="Create recurring slots"
              checked={isRecurring}
              onChange={(e) => setIsRecurring(e.target.checked)}
            />

            {isRecurring && (
              <CanvasSelect
                id="recurring-weeks"
                label="Number of weeks"
                value={recurringWeeks.toString()}
                onChange={(_e, value) => setRecurringWeeks(parseInt(value))}
              >
                {[1, 2, 3, 4, 5, 6, 7, 8].map(weeks => (
                  <option key={weeks} value={weeks.toString()}>
                    {weeks} week{weeks > 1 ? 's' : ''}
                  </option>
                ))}
              </CanvasSelect>
            )}
          </FormFieldGroup>
        </Modal.Body>

        <Modal.Footer>
          <Button onClick={() => setIsAddModalOpen(false)} margin="0 small 0 0">
            Cancel
          </Button>
          <Button
            onClick={handleAddTimeSlot}
            color="primary"
            disabled={loading}
          >
            {loading ? 'Adding...' : 'Add Time Slot'}
          </Button>
        </Modal.Footer>
      </Modal>
    </View>
  )
}

export default FacultyCalendarManager
